// 工作区管理器 - 专注于工作区管理的侧边栏脚本

// 全局状态
let currentWorkspaceId = 'default';
let targetWorkspaceId = null; // 用于指定添加标签页的目标工作区
let allWorkspaces = [];
let currentTabs = [];
let isLoading = false;
let connectionHealthy = true; // 消息连接健康状态
let lastSuccessfulMessage = Date.now(); // 最后一次成功消息时间

// DOM元素
let elements = {};

// 全局错误处理器
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
    showNotification('发生未预期的错误: ' + event.error.message, 'error');
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    showNotification('操作失败: ' + (event.reason?.message || '未知错误'), 'error');
    event.preventDefault();
});

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('🚀 工作区管理器初始化...');

        // 设置全局状态监控
        setupGlobalStateMonitoring();

        // 初始化DOM元素
        initializeElements();

        // 设置事件监听器
        setupEventListeners();

        // 加载初始数据
        await loadInitialData();

        // 启动连接健康监控
        startConnectionHealthMonitor();

        // 启动状态验证定时器
        startStateValidationTimer();

        console.log('✅ 工作区管理器初始化完成');

    } catch (error) {
        console.error('❌ 工作区管理器初始化失败:', error);
        showNotification('初始化失败: ' + error.message, 'error');

        // 尝试恢复
        setTimeout(() => {
            window.location.reload();
        }, 5000);
    }
});

// 设置全局状态监控
function setupGlobalStateMonitoring() {
    // 监控扩展上下文
    setInterval(() => {
        if (!chrome.runtime?.id) {
            console.error('扩展上下文丢失');
            showNotification('扩展需要重新加载', 'error');
        }
    }, 30000);

    // 监控内存使用
    if (performance.memory) {
        setInterval(() => {
            const memoryInfo = performance.memory;
            const usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
            const limitMB = Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024);

            if (usedMB > limitMB * 0.8) {
                console.warn(`内存使用过高: ${usedMB}MB / ${limitMB}MB`);
            }
        }, 60000);
    }
}

// 启动状态验证定时器
function startStateValidationTimer() {
    // 每30秒验证一次状态
    setInterval(async () => {
        try {
            await validateGlobalState();
        } catch (error) {
            console.error('状态验证失败:', error);
        }
    }, 30000);
}

// 验证全局状态
async function validateGlobalState() {
    try {
        // 验证DOM元素
        const missingElements = [];
        for (const [key, element] of Object.entries(elements)) {
            if (!element || !document.contains(element)) {
                missingElements.push(key);
            }
        }

        if (missingElements.length > 0) {
            console.warn('发现缺失的DOM元素:', missingElements);
            // 重新初始化元素
            initializeElements();
        }

        // 验证标签页数据一致性
        if (currentTabs.length > 0) {
            const actualTabs = await chrome.tabs.query({ currentWindow: true });
            if (Math.abs(currentTabs.length - actualTabs.length) > 2) {
                console.warn('标签页数据不一致，重新加载');
                await loadCurrentTabs();
            }
        }

    } catch (error) {
        console.error('全局状态验证失败:', error);
    }
}

// 初始化DOM元素
function initializeElements() {
    elements = {
        // 顶部工作区信息
        currentWorkspaceIcon: document.getElementById('current-workspace-icon'),
        currentWorkspaceName: document.getElementById('current-workspace-name'),
        currentWorkspaceStats: document.getElementById('current-workspace-stats'),
        
        // 按钮
        newWorkspaceBtn: document.getElementById('new-workspace-btn'),
        refreshWorkspacesBtn: document.getElementById('refresh-workspaces-btn'),
        refreshTabsBtn: document.getElementById('refresh-tabs-btn'),
        aiGroupBtn: document.getElementById('ai-group-btn'),
        
        // 列表容器
        workspaceList: document.getElementById('workspace-list'),
        tabGroups: document.getElementById('tab-groups'),
        
        // 添加标签页方法
        addUrlMethod: document.getElementById('add-url-method'),
        addBookmarkMethod: document.getElementById('add-bookmark-method'),
        addCurrentMethod: document.getElementById('add-current-method')
    };
}

// 设置事件监听器
function setupEventListeners() {
    // 防止重复绑定事件
    if (window.eventListenersSetup) {
        return;
    }
    window.eventListenersSetup = true;

    // 新建工作区
    elements.newWorkspaceBtn?.addEventListener('click', createNewWorkspace);

    // 刷新按钮
    elements.refreshWorkspacesBtn?.addEventListener('click', loadWorkspaces);
    elements.refreshTabsBtn?.addEventListener('click', loadCurrentTabs);

    // AI自动分组
    elements.aiGroupBtn?.addEventListener('click', performAIGrouping);

    // 固定验证按钮
    const verifyPinningBtn = document.getElementById('verify-pinning-btn');
    verifyPinningBtn?.addEventListener('click', verifyPinningStatus);

    // 强制应用固定按钮
    const forceApplyPinningBtn = document.getElementById('force-apply-pinning-btn');
    forceApplyPinningBtn?.addEventListener('click', forceApplyPinning);

    // 云同步按钮
    const cloudSyncBtn = document.getElementById('cloud-sync-btn');
    cloudSyncBtn?.addEventListener('click', toggleCloudSync);

    // 同步状态按钮
    const syncStatusBtn = document.getElementById('sync-status-btn');
    syncStatusBtn?.addEventListener('click', showSyncStatus);

    // 添加标签页方法
    elements.addUrlMethod?.addEventListener('click', () => addTabByMethod('url'));
    elements.addBookmarkMethod?.addEventListener('click', () => addTabByMethod('bookmark'));
    elements.addCurrentMethod?.addEventListener('click', () => addTabByMethod('current'));
}

// 加载初始数据（增强错误处理版本）
async function loadInitialData() {
    const loadingSteps = [
        { name: '获取当前工作区', fn: loadCurrentWorkspace },
        { name: '加载工作区列表', fn: loadWorkspaces },
        { name: '加载当前标签页', fn: loadCurrentTabs }
    ];

    try {
        setLoading(true);
        showNotification('正在初始化工作区管理器...', 'info');

        for (let i = 0; i < loadingSteps.length; i++) {
            const step = loadingSteps[i];
            try {
                console.log(`执行步骤 ${i + 1}/${loadingSteps.length}: ${step.name}`);
                await step.fn();
                console.log(`✅ 步骤完成: ${step.name}`);
            } catch (stepError) {
                console.error(`❌ 步骤失败: ${step.name}`, stepError);
                showNotification(`${step.name}失败: ${stepError.message || '未知错误'}`, 'warning');

                // 对于关键步骤，尝试重试
                if (i === 0) { // 获取当前工作区是关键步骤
                    console.log('尝试重试关键步骤...');
                    try {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        await step.fn();
                        console.log(`✅ 重试成功: ${step.name}`);
                    } catch (retryError) {
                        console.error(`❌ 重试失败: ${step.name}`, retryError);
                        showNotification(`${step.name}重试失败，使用默认配置`, 'warning');
                    }
                }
            }
        }

        // 检查连接健康状态
        await checkConnectionHealth();

        showNotification('工作区管理器初始化完成', 'success');

    } catch (error) {
        console.error('❌ 加载初始数据失败:', error);
        showNotification('初始化失败，请刷新页面重试', 'error');

        // 提供恢复选项
        showRecoveryOptions();
    } finally {
        setLoading(false);
    }
}

// 显示恢复选项
function showRecoveryOptions() {
    const recoveryHTML = `
        <div id="recovery-panel" style="
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 16px;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 1500;
        ">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                <span style="color: #dc2626; font-size: 18px;">⚠️</span>
                <strong style="color: #dc2626;">初始化失败</strong>
            </div>
            <p style="margin: 0 0 12px 0; font-size: 13px; color: #7f1d1d;">
                工作区管理器初始化遇到问题，您可以尝试以下恢复选项：
            </p>
            <div style="display: flex; flex-direction: column; gap: 8px;">
                <button id="retry-init" style="
                    padding: 6px 12px;
                    background: #dc2626;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                ">重新初始化</button>
                <button id="reset-data" style="
                    padding: 6px 12px;
                    background: #f59e0b;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                ">重置数据</button>
                <button id="close-recovery" style="
                    padding: 6px 12px;
                    background: #6b7280;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                ">关闭</button>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', recoveryHTML);

    // 绑定恢复选项事件
    document.getElementById('retry-init')?.addEventListener('click', async () => {
        document.getElementById('recovery-panel')?.remove();
        await loadInitialData();
    });

    document.getElementById('reset-data')?.addEventListener('click', async () => {
        if (confirm('确定要重置所有数据吗？这将清除所有工作区配置。')) {
            document.getElementById('recovery-panel')?.remove();
            await resetAllData();
        }
    });

    document.getElementById('close-recovery')?.addEventListener('click', () => {
        document.getElementById('recovery-panel')?.remove();
    });

    // 10秒后自动关闭
    setTimeout(() => {
        document.getElementById('recovery-panel')?.remove();
    }, 10000);
}

// 重置所有数据
async function resetAllData() {
    try {
        setLoading(true);
        showNotification('正在重置数据...', 'info');

        // 清除本地存储
        await chrome.storage.local.clear();
        await chrome.storage.sync.clear();

        showNotification('数据已重置，正在重新初始化...', 'info');

        // 重新初始化
        await new Promise(resolve => setTimeout(resolve, 1000));
        await loadInitialData();

    } catch (error) {
        console.error('重置数据失败:', error);
        showNotification('重置数据失败', 'error');
    } finally {
        setLoading(false);
    }
}

// 加载当前工作区信息
async function loadCurrentWorkspace() {
    try {
        const response = await sendMessage('GET_CURRENT_WORKSPACE');
        if (response.success) {
            currentWorkspaceId = response.workspaceId || 'default';
            
            // 获取工作区详细信息
            const workspaceResponse = await sendMessage('GET_WORKSPACE', { workspaceId: currentWorkspaceId });
            if (workspaceResponse.success && workspaceResponse.workspace) {
                updateCurrentWorkspaceDisplay(workspaceResponse.workspace);
            }
        }
    } catch (error) {
        console.error('加载当前工作区失败:', error);
    }
}

// 更新当前工作区显示
function updateCurrentWorkspaceDisplay(workspace) {
    if (elements.currentWorkspaceIcon) {
        elements.currentWorkspaceIcon.textContent = workspace.icon || '🏠';
    }
    
    if (elements.currentWorkspaceName) {
        elements.currentWorkspaceName.textContent = workspace.name || '默认工作区';
    }
    
    if (elements.currentWorkspaceStats) {
        const tabCount = workspace.groups?.reduce((total, group) => total + group.tabs.length, 0) || 0;
        elements.currentWorkspaceStats.textContent = `${tabCount} 个标签页`;
    }
}

// 加载工作区列表
async function loadWorkspaces() {
    try {
        const response = await sendMessage('GET_WORKSPACES');
        if (response.success) {
            allWorkspaces = response.workspaces || [];
            renderWorkspaceList();
        }
    } catch (error) {
        console.error('加载工作区列表失败:', error);
        showError(elements.workspaceList, '加载工作区失败');
    }
}

// 渲染工作区列表
function renderWorkspaceList() {
    if (!elements.workspaceList) return;
    
    if (allWorkspaces.length === 0) {
        elements.workspaceList.innerHTML = `
            <div class="loading">
                <div class="loading-spinner"></div>
                暂无工作区
            </div>
        `;
        return;
    }
    
    elements.workspaceList.innerHTML = allWorkspaces.map(workspace => `
        <div class="workspace-item ${workspace.id === currentWorkspaceId ? 'active' : ''}"
             data-workspace-id="${workspace.id}">
            <div class="workspace-item-main">
                <div class="workspace-item-info">
                    <div class="workspace-item-icon" style="background-color: ${workspace.color}20; color: ${workspace.color};">
                        ${workspace.icon || '📁'}
                    </div>
                    <div class="workspace-item-details">
                        <div class="workspace-item-name">${workspace.name}</div>
                        <div class="workspace-item-count">
                            ${workspace.groups?.reduce((total, group) => total + group.tabs.length, 0) || 0} 个标签页
                        </div>
                    </div>
                </div>
                <div class="workspace-item-actions">
                    <button class="btn btn-small btn-secondary workspace-view-btn"
                            data-workspace-id="${workspace.id}">
                        查看
                    </button>
                    <button class="btn btn-small btn-secondary workspace-add-btn"
                            data-workspace-id="${workspace.id}">
                        添加
                    </button>
                    ${workspace.id !== currentWorkspaceId ? `
                    <button class="btn btn-small btn-secondary workspace-switch-btn"
                            data-workspace-id="${workspace.id}">
                        切换
                    </button>
                    ` : `
                    <button class="btn btn-small btn-primary workspace-current-btn" disabled>
                        当前
                    </button>
                    `}
                    ${!workspace.isDefault ? `
                    <button class="btn btn-small btn-danger workspace-delete-btn"
                            data-workspace-id="${workspace.id}"
                            data-workspace-name="${workspace.name}">
                        删除
                    </button>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('');

    // 绑定工作区按钮事件
    bindWorkspaceEvents();
}

// 绑定工作区事件
function bindWorkspaceEvents() {
    // 绑定查看按钮事件
    const viewButtons = document.querySelectorAll('.workspace-view-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const workspaceId = button.dataset.workspaceId;
            if (workspaceId && window.viewWorkspace) {
                window.viewWorkspace(workspaceId);
            }
        });
    });

    // 绑定切换按钮事件
    const switchButtons = document.querySelectorAll('.workspace-switch-btn');
    switchButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const workspaceId = button.dataset.workspaceId;
            if (workspaceId && window.switchToWorkspace) {
                window.switchToWorkspace(workspaceId);
            }
        });
    });

    // 绑定添加按钮事件
    const addButtons = document.querySelectorAll('.workspace-add-btn');
    addButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const workspaceId = button.dataset.workspaceId;
            if (workspaceId) {
                toggleWorkspaceAddPanel(workspaceId, button);
            }
        });
    });

    // 绑定删除按钮事件
    const deleteButtons = document.querySelectorAll('.workspace-delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const workspaceId = button.dataset.workspaceId;
            const workspaceName = button.dataset.workspaceName;
            if (workspaceId && workspaceName) {
                showDeleteWorkspaceConfirmation(workspaceId, workspaceName);
            }
        });
    });
}

// 切换工作区添加面板
function toggleWorkspaceAddPanel(workspaceId, button) {
    const workspaceItem = button.closest('.workspace-item');
    const existingPanel = workspaceItem.querySelector('.workspace-add-panel');

    // 如果面板已存在，则关闭
    if (existingPanel) {
        existingPanel.remove();
        button.textContent = '添加';
        return;
    }

    // 创建添加面板
    const panelHTML = `
        <div class="workspace-add-panel">
            <div class="add-panel-header">
                <h4>添加标签页到工作区</h4>
                <button class="add-panel-close">✕</button>
            </div>
            <div class="add-panel-content">
                <div class="add-method-buttons">
                    <button class="add-method-btn" data-method="url" data-workspace-id="${workspaceId}">
                        <span class="method-icon">🔗</span>
                        <span class="method-text">输入网址</span>
                    </button>
                    <button class="add-method-btn" data-method="bookmark" data-workspace-id="${workspaceId}">
                        <span class="method-icon">⭐</span>
                        <span class="method-text">从书签选择</span>
                    </button>
                    <button class="add-method-btn" data-method="current" data-workspace-id="${workspaceId}">
                        <span class="method-icon">📄</span>
                        <span class="method-text">当前标签页</span>
                    </button>
                </div>
            </div>
        </div>
    `;

    // 插入面板
    workspaceItem.insertAdjacentHTML('beforeend', panelHTML);
    button.textContent = '收起';

    // 绑定面板事件
    bindAddPanelEvents(workspaceItem, button);
}

// 绑定添加面板事件
function bindAddPanelEvents(workspaceItem, toggleButton) {
    const panel = workspaceItem.querySelector('.workspace-add-panel');
    const closeBtn = panel.querySelector('.add-panel-close');
    const methodButtons = panel.querySelectorAll('.add-method-btn');

    // 关闭面板
    const closePanel = () => {
        panel.remove();
        toggleButton.textContent = '添加';
    };

    closeBtn.addEventListener('click', closePanel);

    // 方法按钮事件
    methodButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const method = btn.dataset.method;
            const workspaceId = btn.dataset.workspaceId;

            // 设置目标工作区
            targetWorkspaceId = workspaceId;

            // 调用对应的添加方法
            addTabByMethod(method);

            // 关闭面板
            closePanel();
        });
    });
}

// 加载当前标签页（增强版本，支持状态验证和同步）
async function loadCurrentTabs() {
    try {
        console.log('🔄 开始加载当前标签页...');

        // 获取标签页数据
        const response = await sendMessage('GET_CURRENT_TABS');
        if (response.success) {
            const newTabs = response.tabs || [];
            console.log(`📋 获取到 ${newTabs.length} 个标签页`);

            // 验证标签页状态一致性
            const verifiedTabs = await verifyTabStatesConsistency(newTabs);

            // 更新全局状态
            currentTabs = verifiedTabs;

            // 渲染标签页列表
            renderPinnedTabs();

            console.log('✅ 标签页加载完成');
        } else {
            throw new Error(response.error || '获取标签页失败');
        }
    } catch (error) {
        console.error('❌ 加载当前标签页失败:', error);
        showError(elements.tabGroups, '加载标签页失败: ' + error.message);

        // 尝试恢复机制
        await attemptTabLoadRecovery();
    }
}

// 验证标签页状态一致性
async function verifyTabStatesConsistency(tabs) {
    try {
        console.log('🔍 验证标签页状态一致性...');

        const verifiedTabs = [];
        let inconsistencyCount = 0;

        for (const tab of tabs) {
            if (!tab.id) {
                console.warn('跳过无效标签页:', tab);
                continue;
            }

            try {
                // 直接从Chrome API获取最新状态
                const actualTab = await chrome.tabs.get(tab.id);

                // 检查状态一致性
                const hasInconsistency =
                    tab.pinned !== actualTab.pinned ||
                    tab.title !== actualTab.title ||
                    tab.url !== actualTab.url;

                if (hasInconsistency) {
                    inconsistencyCount++;
                    console.warn(`发现状态不一致的标签页: ${tab.title}`, {
                        cached: { pinned: tab.pinned, title: tab.title },
                        actual: { pinned: actualTab.pinned, title: actualTab.title }
                    });

                    // 使用实际状态
                    verifiedTabs.push({
                        ...tab,
                        pinned: actualTab.pinned,
                        title: actualTab.title,
                        url: actualTab.url,
                        favIconUrl: actualTab.favIconUrl,
                        _verified: true,
                        _corrected: true
                    });
                } else {
                    verifiedTabs.push({
                        ...tab,
                        _verified: true,
                        _corrected: false
                    });
                }
            } catch (tabError) {
                console.warn(`无法验证标签页 ${tab.id}:`, tabError);
                // 标签页可能已被关闭，跳过
                continue;
            }
        }

        if (inconsistencyCount > 0) {
            console.warn(`⚠️ 发现 ${inconsistencyCount} 个状态不一致的标签页，已自动修正`);
            showNotification(`已修正 ${inconsistencyCount} 个标签页状态不一致`, 'warning');
        }

        console.log(`✅ 状态验证完成: ${verifiedTabs.length} 个有效标签页`);
        return verifiedTabs;

    } catch (error) {
        console.error('验证标签页状态失败:', error);
        return tabs; // 返回原始数据作为后备
    }
}

// 尝试标签页加载恢复
async function attemptTabLoadRecovery() {
    try {
        console.log('🔄 尝试标签页加载恢复...');

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 直接从Chrome API获取标签页
        const tabs = await chrome.tabs.query({ currentWindow: true });

        if (tabs && tabs.length > 0) {
            console.log(`🔄 恢复成功，获取到 ${tabs.length} 个标签页`);
            currentTabs = tabs;
            renderPinnedTabs();
            showNotification('标签页列表已恢复', 'success');
        } else {
            throw new Error('无法获取标签页');
        }
    } catch (error) {
        console.error('标签页加载恢复失败:', error);
        showNotification('标签页加载失败，请刷新页面', 'error');
    }
}

// 渲染固定标签页（增强版本，支持状态验证和实时更新）
function renderPinnedTabs() {
    if (!elements.tabGroups) return;

    try {
        console.log('🎨 开始渲染标签页列表...');

        if (currentTabs.length === 0) {
            elements.tabGroups.innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    暂无标签页
                </div>
            `;
            return;
        }

        // 验证并清理标签页数据
        const validTabs = currentTabs.filter(tab => tab && tab.id && tab.url);

        if (validTabs.length !== currentTabs.length) {
            console.warn(`过滤掉 ${currentTabs.length - validTabs.length} 个无效标签页`);
        }

        // 按固定状态组织标签页
        const pinnedTabs = validTabs.filter(tab => tab.pinned === true);
        const regularTabs = validTabs.filter(tab => tab.pinned !== true);

        console.log(`📊 标签页统计: 固定 ${pinnedTabs.length} 个, 普通 ${regularTabs.length} 个`);

        let html = '';

        // 固定标签页部分
        if (pinnedTabs.length > 0) {
            html += `
                <div class="tab-section" data-section="pinned">
                    <div class="tab-section-header">
                        <div class="tab-section-info">
                            <span class="tab-section-icon">📌</span>
                            <span class="tab-section-name">固定标签页</span>
                            <span class="tab-section-count">(${pinnedTabs.length})</span>
                        </div>
                        <div class="tab-section-actions">
                            <button class="btn btn-small refresh-section-btn" data-section="pinned" title="刷新固定标签页">
                                🔄
                            </button>
                        </div>
                    </div>
                    <div class="tab-list">
                        ${pinnedTabs.map(tab => renderTabItem(tab, true)).join('')}
                    </div>
                </div>
            `;
        }

        // 普通标签页部分
        if (regularTabs.length > 0) {
            html += `
                <div class="tab-section" data-section="regular">
                    <div class="tab-section-header">
                        <div class="tab-section-info">
                            <span class="tab-section-icon">📄</span>
                            <span class="tab-section-name">普通标签页</span>
                            <span class="tab-section-count">(${regularTabs.length})</span>
                        </div>
                        <div class="tab-section-actions">
                            <button class="btn btn-small refresh-section-btn" data-section="regular" title="刷新普通标签页">
                                🔄
                            </button>
                        </div>
                    </div>
                    <div class="tab-list">
                        ${regularTabs.map(tab => renderTabItem(tab, false)).join('')}
                    </div>
                </div>
            `;
        }

        // 如果没有任何标签页
        if (pinnedTabs.length === 0 && regularTabs.length === 0) {
            html = `
                <div class="empty-state">
                    <div class="empty-state-icon">📭</div>
                    <div class="empty-state-title">暂无标签页</div>
                    <div class="empty-state-description">当前窗口没有打开的标签页</div>
                    <button class="btn btn-primary refresh-tabs-btn">刷新标签页</button>
                </div>
            `;
        }

        elements.tabGroups.innerHTML = html;

        // 绑定标签页事件
        bindTabEvents();

        // 绑定刷新按钮事件
        bindRefreshSectionEvents();

        console.log('✅ 标签页列表渲染完成');

    } catch (error) {
        console.error('❌ 渲染标签页列表失败:', error);
        elements.tabGroups.innerHTML = `
            <div class="error-state">
                <div class="error-state-icon">❌</div>
                <div class="error-state-title">渲染失败</div>
                <div class="error-state-description">${error.message}</div>
                <button class="btn btn-primary refresh-tabs-btn">重新加载</button>
            </div>
        `;
    }
}

// 绑定刷新区域按钮事件
function bindRefreshSectionEvents() {
    const refreshButtons = document.querySelectorAll('.refresh-section-btn');
    refreshButtons.forEach(button => {
        button.addEventListener('click', async (e) => {
            e.stopPropagation();
            const section = button.dataset.section;
            console.log(`🔄 刷新 ${section} 区域...`);

            // 显示加载状态
            button.textContent = '⏳';
            button.disabled = true;

            try {
                await loadCurrentTabs();
                showNotification(`${section === 'pinned' ? '固定' : '普通'}标签页已刷新`, 'success');
            } catch (error) {
                console.error('刷新区域失败:', error);
                showNotification('刷新失败', 'error');
            } finally {
                button.textContent = '🔄';
                button.disabled = false;
            }
        });
    });

    // 绑定空状态刷新按钮
    const emptyRefreshBtn = document.querySelector('.refresh-tabs-btn');
    if (emptyRefreshBtn) {
        emptyRefreshBtn.addEventListener('click', async () => {
            try {
                await loadCurrentTabs();
            } catch (error) {
                console.error('刷新标签页失败:', error);
            }
        });
    }
}

// 渲染单个标签页项目（增强版本，支持状态验证和错误处理）
function renderTabItem(tab, expectedPinned = null) {
    try {
        // 验证标签页数据完整性
        if (!tab || !tab.id || !tab.url) {
            console.warn('跳过无效标签页:', tab);
            return '';
        }

        // 状态验证：如果提供了期望的固定状态，进行验证
        const actualPinned = tab.pinned === true;
        let statusIndicator = '';
        let statusClass = '';

        if (expectedPinned !== null && expectedPinned !== actualPinned) {
            statusIndicator = `<span class="status-warning" title="状态可能不一致">⚠️</span>`;
            statusClass = 'status-inconsistent';
            console.warn(`标签页 ${tab.title} 状态不一致: 期望 ${expectedPinned}, 实际 ${actualPinned}`);
        }

        // 处理标题和URL
        const safeTitle = escapeHtml(tab.title || '未知标题');
        const safeUrl = escapeHtml(tab.url);
        let hostname = '';

        try {
            hostname = new URL(tab.url).hostname;
        } catch (urlError) {
            hostname = tab.url;
            console.warn('解析URL失败:', tab.url);
        }

        // 处理图标
        const faviconUrl = tab.favIconUrl || 'data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 16 16%22%3E%3Crect width=%2216%22 height=%2216%22 fill=%22%23f3f4f6%22/%3E%3C/svg%3E';

        // 状态指示器
        const verificationStatus = tab._verified ?
            (tab._corrected ?
                `<span class="verification-status corrected" title="状态已修正">🔧</span>` :
                `<span class="verification-status verified" title="状态已验证">✅</span>`
            ) :
            `<span class="verification-status unverified" title="状态未验证">❓</span>`;

        return `
            <div class="tab-item ${statusClass}"
                 data-tab-id="${tab.id}"
                 data-tab-url="${safeUrl}"
                 data-verified="${tab._verified || false}"
                 data-corrected="${tab._corrected || false}">
                <img class="tab-favicon"
                     src="${faviconUrl}"
                     alt="favicon"
                     onerror="this.src='data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 16 16%22%3E%3Crect width=%2216%22 height=%2216%22 fill=%22%23f3f4f6%22/%3E%3C/svg%3E'">
                <div class="tab-info">
                    <div class="tab-title">
                        ${safeTitle}
                        ${statusIndicator}
                        ${verificationStatus}
                    </div>
                    <div class="tab-url" title="${safeUrl}">${hostname}</div>
                    ${tab._lastUpdated ? `<div class="tab-meta">更新: ${new Date(tab._lastUpdated).toLocaleTimeString()}</div>` : ''}
                </div>
                <div class="tab-actions">
                    <button class="tab-pin-btn ${actualPinned ? 'pinned' : ''}"
                            data-tab-id="${tab.id}"
                            data-pinned="${actualPinned}"
                            title="${actualPinned ? '取消固定' : '固定标签页'}"
                            aria-label="${actualPinned ? '取消固定标签页' : '固定标签页'}">
                        <span class="pin-icon">${actualPinned ? '📌' : '📍'}</span>
                    </button>
                    <button class="tab-verify-btn"
                            data-tab-id="${tab.id}"
                            title="验证标签页状态"
                            aria-label="验证标签页状态">
                        <span class="verify-icon">🔍</span>
                    </button>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('渲染标签页项目失败:', error, tab);
        return `
            <div class="tab-item error" data-tab-id="${tab?.id || 'unknown'}">
                <div class="tab-info">
                    <div class="tab-title error">渲染失败: ${error.message}</div>
                    <div class="tab-url">${tab?.url || '未知URL'}</div>
                </div>
            </div>
        `;
    }
}

// HTML转义函数
function escapeHtml(text) {
    if (typeof text !== 'string') return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 绑定标签页事件（增强版本，支持验证和错误处理）
function bindTabEvents() {
    try {
        console.log('🔗 绑定标签页事件...');

        const tabItems = document.querySelectorAll('.tab-item');
        console.log(`找到 ${tabItems.length} 个标签页项目`);

        // 绑定标签页点击事件
        tabItems.forEach((tabItem, index) => {
            // 移除旧的事件监听器（如果存在）
            const newTabItem = tabItem.cloneNode(true);
            tabItem.parentNode.replaceChild(newTabItem, tabItem);

            newTabItem.addEventListener('click', (e) => {
                // 如果点击的是按钮，不触发标签页切换
                if (e.target.closest('.tab-pin-btn') || e.target.closest('.tab-verify-btn')) {
                    return;
                }

                const tabId = parseInt(newTabItem.dataset.tabId);
                if (tabId && window.switchToTab) {
                    console.log(`🔄 切换到标签页: ${tabId}`);
                    window.switchToTab(tabId);
                } else {
                    console.warn('无效的标签页ID:', tabId);
                }
            });
        });

        // 重新获取更新后的元素并绑定按钮事件
        const updatedTabItems = document.querySelectorAll('.tab-item');

        // 绑定固定按钮事件
        const pinButtons = document.querySelectorAll('.tab-pin-btn');
        console.log(`找到 ${pinButtons.length} 个固定按钮`);

        pinButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.stopPropagation();

                const tabId = parseInt(button.dataset.tabId);
                const isPinned = button.dataset.pinned === 'true';

                if (tabId) {
                    console.log(`🔄 切换标签页 ${tabId} 固定状态: ${!isPinned}`);

                    // 显示加载状态
                    const originalContent = button.innerHTML;
                    button.innerHTML = '<span class="pin-icon">⏳</span>';
                    button.disabled = true;

                    try {
                        await toggleTabPinned(tabId, !isPinned);
                    } catch (error) {
                        console.error('固定操作失败:', error);
                        // 恢复按钮状态
                        button.innerHTML = originalContent;
                    } finally {
                        button.disabled = false;
                    }
                } else {
                    console.warn('无效的标签页ID:', tabId);
                    showNotification('无效的标签页', 'error');
                }
            });
        });

        // 绑定验证按钮事件
        const verifyButtons = document.querySelectorAll('.tab-verify-btn');
        console.log(`找到 ${verifyButtons.length} 个验证按钮`);

        verifyButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.stopPropagation();

                const tabId = parseInt(button.dataset.tabId);
                if (tabId) {
                    await verifyAndFixTabState(tabId, button);
                } else {
                    console.warn('无效的标签页ID:', tabId);
                }
            });
        });

        console.log('✅ 标签页事件绑定完成');

    } catch (error) {
        console.error('❌ 绑定标签页事件失败:', error);
    }
}

// 验证并修复标签页状态
async function verifyAndFixTabState(tabId, button) {
    try {
        console.log(`🔍 验证标签页 ${tabId} 状态...`);

        // 显示加载状态
        const originalContent = button.innerHTML;
        button.innerHTML = '<span class="verify-icon">⏳</span>';
        button.disabled = true;

        // 从Chrome API获取实际状态
        const actualTab = await chrome.tabs.get(tabId);

        // 从当前显示的数据获取期望状态
        const tabItem = button.closest('.tab-item');
        const displayedPinned = tabItem.querySelector('.tab-pin-btn').dataset.pinned === 'true';

        // 比较状态
        const actualPinned = actualTab.pinned || false;
        const isConsistent = actualPinned === displayedPinned;

        if (isConsistent) {
            showNotification('标签页状态一致', 'success');
            console.log(`✅ 标签页 ${tabId} 状态一致`);
        } else {
            console.warn(`⚠️ 标签页 ${tabId} 状态不一致: 显示 ${displayedPinned}, 实际 ${actualPinned}`);

            // 修复状态
            updateTabPinningUI(tabId, actualPinned);
            showNotification(`已修复标签页状态: ${actualPinned ? '固定' : '未固定'}`, 'warning');
        }

    } catch (error) {
        console.error(`验证标签页 ${tabId} 失败:`, error);
        showNotification('验证失败: ' + error.message, 'error');
    } finally {
        // 恢复按钮状态
        button.innerHTML = originalContent;
        button.disabled = false;
    }
}

// 分组展开/收起功能已移除，改用固定状态管理

// 按分组组织标签页
function organizeTabsByGroups(tabs) {
    const groups = new Map();
    
    tabs.forEach(tab => {
        const groupId = tab.groupId || -1;
        const groupName = groupId === -1 ? '未分组' : `分组 ${groupId}`;
        const groupColor = getGroupColor(groupId);
        
        if (!groups.has(groupId)) {
            groups.set(groupId, {
                id: groupId,
                name: groupName,
                color: groupColor,
                tabs: []
            });
        }
        
        groups.get(groupId).tabs.push(tab);
    });
    
    return Array.from(groups.values());
}

// 获取分组颜色
function getGroupColor(groupId) {
    const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'];
    return groupId === -1 ? '#6b7280' : colors[groupId % colors.length];
}

// 工具函数
function setLoading(loading) {
    isLoading = loading;
}

function showError(container, message) {
    if (container) {
        container.innerHTML = `
            <div class="loading">
                ❌ ${message}
            </div>
        `;
    }
}

function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 确定通知样式
    const styles = {
        success: { bg: '#dcfce7', border: '#16a34a', text: '#166534', icon: '✅' },
        error: { bg: '#fef2f2', border: '#ef4444', text: '#dc2626', icon: '❌' },
        info: { bg: '#f0f9ff', border: '#0ea5e9', text: '#0369a1', icon: 'ℹ️' },
        warning: { bg: '#fefce8', border: '#eab308', text: '#a16207', icon: '⚠️' }
    };

    const style = styles[type] || styles.info;

    // 创建通知HTML
    const notificationHTML = `
        <div id="notification" style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${style.bg};
            border: 1px solid ${style.border};
            color: ${style.text};
            padding: 12px 16px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 2000;
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 300px;
            font-size: 14px;
            font-weight: 500;
            animation: slideIn 0.3s ease-out;
        ">
            <span>${style.icon}</span>
            <span>${message}</span>
            <button class="notification-close" style="
                background: none;
                border: none;
                color: ${style.text};
                cursor: pointer;
                padding: 0;
                margin-left: 8px;
                font-size: 16px;
                opacity: 0.7;
            ">✕</button>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', notificationHTML);

    // 添加关闭按钮事件监听器
    const notification = document.getElementById('notification');
    const closeBtn = notification.querySelector('.notification-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });
    }

    // 添加动画样式（如果还没有）
    if (!document.getElementById('notification-styles')) {
        const styleElement = document.createElement('style');
        styleElement.id = 'notification-styles';
        styleElement.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(styleElement);
    }

    // 自动移除通知
    setTimeout(() => {
        const notification = document.getElementById('notification');
        if (notification) {
            notification.style.animation = 'slideOut 0.3s ease-in forwards';
            setTimeout(() => notification.remove(), 300);
        }
    }, 4000);

    console.log(`[${type.toUpperCase()}] ${message}`);
}

// 增强的错误处理和恢复机制
class ErrorRecoveryManager {
    constructor() {
        this.errorHistory = [];
        this.recoveryStrategies = new Map();
        this.maxErrorHistory = 50;

        // 注册默认恢复策略
        this.registerRecoveryStrategies();
    }

    // 注册恢复策略
    registerRecoveryStrategies() {
        this.recoveryStrategies.set('EXTENSION_CONTEXT_INVALID', {
            name: '扩展上下文无效',
            handler: () => this.handleExtensionContextError(),
            autoRetry: false
        });

        this.recoveryStrategies.set('TAB_OPERATION_FAILED', {
            name: '标签页操作失败',
            handler: () => this.handleTabOperationError(),
            autoRetry: true,
            maxRetries: 2
        });

        this.recoveryStrategies.set('STATE_INCONSISTENCY', {
            name: '状态不一致',
            handler: () => this.handleStateInconsistency(),
            autoRetry: true,
            maxRetries: 1
        });

        this.recoveryStrategies.set('STORAGE_ERROR', {
            name: '存储错误',
            handler: () => this.handleStorageError(),
            autoRetry: true,
            maxRetries: 3
        });
    }

    // 处理错误
    async handleError(error, context = {}) {
        const errorInfo = {
            message: error.message,
            stack: error.stack,
            context,
            timestamp: Date.now(),
            id: Date.now() + Math.random()
        };

        // 添加到错误历史
        this.addToErrorHistory(errorInfo);

        // 确定错误类型
        const errorType = this.classifyError(error, context);

        // 获取恢复策略
        const strategy = this.recoveryStrategies.get(errorType);

        if (strategy) {
            console.log(`🔧 尝试恢复策略: ${strategy.name}`);

            try {
                const result = await strategy.handler();

                if (result.success) {
                    showNotification(`已自动恢复: ${strategy.name}`, 'success');
                    return { recovered: true, strategy: strategy.name };
                } else {
                    throw new Error(result.error || '恢复失败');
                }
            } catch (recoveryError) {
                console.error('恢复策略失败:', recoveryError);

                // 如果支持自动重试且未超过最大重试次数
                if (strategy.autoRetry && context.retryCount < (strategy.maxRetries || 1)) {
                    console.log(`🔄 自动重试 (${context.retryCount + 1}/${strategy.maxRetries})`);
                    return { recovered: false, shouldRetry: true };
                }
            }
        }

        // 显示用户友好的错误信息
        this.showUserFriendlyError(errorType, error, context);

        return { recovered: false, shouldRetry: false };
    }

    // 分类错误
    classifyError(error, context) {
        const message = error.message.toLowerCase();

        if (message.includes('extension context') || message.includes('runtime.id')) {
            return 'EXTENSION_CONTEXT_INVALID';
        }

        if (context.operation && context.operation.includes('tab')) {
            return 'TAB_OPERATION_FAILED';
        }

        if (message.includes('state') || message.includes('inconsistent')) {
            return 'STATE_INCONSISTENCY';
        }

        if (message.includes('storage') || message.includes('quota')) {
            return 'STORAGE_ERROR';
        }

        return 'UNKNOWN_ERROR';
    }

    // 处理扩展上下文错误
    async handleExtensionContextError() {
        try {
            // 尝试重新连接
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (chrome.runtime?.id) {
                return { success: true };
            } else {
                return { success: false, error: '扩展需要重新加载' };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 处理标签页操作错误
    async handleTabOperationError() {
        try {
            // 重新加载标签页数据
            await loadCurrentTabs();
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 处理状态不一致
    async handleStateInconsistency() {
        try {
            // 强制重新同步状态
            await loadCurrentTabs();
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 处理存储错误
    async handleStorageError() {
        try {
            // 清理存储并重新初始化
            await chrome.storage.local.clear();
            await loadInitialData();
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 添加到错误历史
    addToErrorHistory(errorInfo) {
        this.errorHistory.push(errorInfo);

        // 保持历史记录在限制内
        if (this.errorHistory.length > this.maxErrorHistory) {
            this.errorHistory.shift();
        }
    }

    // 显示用户友好的错误信息
    showUserFriendlyError(errorType, error, context) {
        const friendlyMessages = {
            'EXTENSION_CONTEXT_INVALID': '扩展需要重新加载才能正常工作',
            'TAB_OPERATION_FAILED': '标签页操作失败，请稍后重试',
            'STATE_INCONSISTENCY': '数据状态不一致，已尝试自动修复',
            'STORAGE_ERROR': '数据存储出现问题，请检查浏览器设置',
            'UNKNOWN_ERROR': '发生未知错误，请联系技术支持'
        };

        const friendlyMessage = friendlyMessages[errorType] || friendlyMessages['UNKNOWN_ERROR'];

        showNotification(friendlyMessage, 'error');

        // 记录详细错误信息到控制台
        console.error(`错误类型: ${errorType}`, {
            error: error.message,
            context,
            timestamp: new Date().toISOString()
        });
    }

    // 获取错误统计
    getErrorStats() {
        const now = Date.now();
        const last24Hours = this.errorHistory.filter(error => now - error.timestamp < 24 * 60 * 60 * 1000);
        const lastHour = this.errorHistory.filter(error => now - error.timestamp < 60 * 60 * 1000);

        return {
            total: this.errorHistory.length,
            last24Hours: last24Hours.length,
            lastHour: lastHour.length,
            mostCommon: this.getMostCommonError()
        };
    }

    // 获取最常见的错误
    getMostCommonError() {
        const errorCounts = {};

        this.errorHistory.forEach(error => {
            const key = error.message;
            errorCounts[key] = (errorCounts[key] || 0) + 1;
        });

        let mostCommon = null;
        let maxCount = 0;

        for (const [message, count] of Object.entries(errorCounts)) {
            if (count > maxCount) {
                maxCount = count;
                mostCommon = message;
            }
        }

        return { message: mostCommon, count: maxCount };
    }
}

// 创建全局错误恢复管理器实例
const errorRecoveryManager = new ErrorRecoveryManager();

// 发送消息到background script（增强版本，支持重试和更好的错误处理）
async function sendMessage(action, data = {}, retryCount = 3) {
    return new Promise(async (resolve) => {
        console.log('发送消息:', action, data, `(重试次数: ${3 - retryCount + 1})`);

        // 检查扩展上下文是否有效
        if (!chrome.runtime?.id) {
            console.error('扩展上下文无效，可能需要重新加载扩展');
            resolve({
                success: false,
                error: '扩展上下文无效，请重新加载扩展',
                needsReload: true
            });
            return;
        }

        try {
            // 使用超时机制防止消息挂起
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('消息发送超时')), 10000); // 10秒超时
            });

            const messagePromise = new Promise((messageResolve) => {
                chrome.runtime.sendMessage({ action, ...data }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error('消息发送错误:', chrome.runtime.lastError);

                        // 检查是否是端口关闭错误
                        const isPortClosedError = chrome.runtime.lastError.message?.includes('message port closed') ||
                                                chrome.runtime.lastError.message?.includes('receiving end does not exist');

                        messageResolve({
                            success: false,
                            error: chrome.runtime.lastError.message,
                            isPortError: isPortClosedError
                        });
                        return;
                    }

                    console.log('收到响应:', action, response);
                    messageResolve(response || { success: false, error: 'No response' });
                });
            });

            const result = await Promise.race([messagePromise, timeoutPromise]);

            // 更新连接健康状态
            if (result.success) {
                connectionHealthy = true;
                lastSuccessfulMessage = Date.now();
                updateConnectionStatus();
            } else if (result.isPortError) {
                connectionHealthy = false;
                updateConnectionStatus();
            }

            // 如果是端口错误且还有重试次数，则重试
            if (result.isPortError && retryCount > 0) {
                console.warn(`端口错误，${1000}ms后重试...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return await sendMessage(action, data, retryCount - 1);
            }

            resolve(result);

        } catch (error) {
            console.error('消息发送异常:', error);

            // 如果还有重试次数，则重试
            if (retryCount > 0) {
                console.warn(`发送失败，${1000}ms后重试...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return await sendMessage(action, data, retryCount - 1);
            }

            resolve({
                success: false,
                error: error.message || '消息发送失败',
                isTimeout: error.message?.includes('超时')
            });
        }
    });
}

// 连接健康检查和恢复机制
function startConnectionHealthMonitor() {
    // 每30秒检查一次连接健康状态
    setInterval(async () => {
        const timeSinceLastSuccess = Date.now() - lastSuccessfulMessage;

        // 如果超过2分钟没有成功消息，进行健康检查
        if (timeSinceLastSuccess > 120000) {
            console.log('执行连接健康检查...');
            await checkConnectionHealth();
        }
    }, 30000);
}

// 检查连接健康状态
async function checkConnectionHealth() {
    try {
        const healthCheck = await sendMessage('HEALTH_CHECK', {}, 1); // 只重试1次

        if (healthCheck.success) {
            if (!connectionHealthy) {
                console.log('连接已恢复');
                connectionHealthy = true;
                showNotification('连接已恢复', 'success');
            }
        } else {
            if (connectionHealthy) {
                console.warn('连接出现问题');
                connectionHealthy = false;
                showNotification('连接出现问题，正在尝试恢复...', 'warning');
            }
        }
    } catch (error) {
        console.error('健康检查失败:', error);
        connectionHealthy = false;
    }
}

// 显示连接状态指示器
function updateConnectionStatus() {
    const statusIndicator = document.getElementById('connection-status');
    if (statusIndicator) {
        statusIndicator.className = connectionHealthy ? 'connection-healthy' : 'connection-unhealthy';
        statusIndicator.title = connectionHealthy ? '连接正常' : '连接异常';
    }
}

// 显示删除工作区确认对话框
function showDeleteWorkspaceConfirmation(workspaceId, workspaceName) {
    // 移除现有确认对话框
    const existingModal = document.getElementById('delete-workspace-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建确认对话框HTML
    const modalHTML = `
        <div id="delete-workspace-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 24px;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            ">
                <div style="
                    display: flex;
                    align-items: center;
                    margin-bottom: 16px;
                ">
                    <div style="
                        width: 40px;
                        height: 40px;
                        background: #fef2f2;
                        color: #dc2626;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 20px;
                        margin-right: 12px;
                    ">⚠️</div>
                    <h3 style="margin: 0; font-size: 18px; color: #1e293b;">删除工作区</h3>
                </div>

                <p style="
                    margin: 0 0 20px 0;
                    color: #64748b;
                    line-height: 1.5;
                ">
                    确定要删除工作区 <strong style="color: #1e293b;">"${workspaceName}"</strong> 吗？
                    <br><br>
                    此操作将：
                    <br>• 删除工作区及其所有分组
                    <br>• 移除工作区中保存的标签页记录
                    <br>• 此操作不可撤销
                </p>

                <div style="
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                ">
                    <button id="cancel-delete-workspace" style="
                        padding: 10px 20px;
                        background: #e2e8f0;
                        color: #475569;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">取消</button>
                    <button id="confirm-delete-workspace" data-workspace-id="${workspaceId}" style="
                        padding: 10px 20px;
                        background: #dc2626;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">确认删除</button>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 设置事件监听器
    setupDeleteWorkspaceEvents();
}

// 设置删除工作区事件
function setupDeleteWorkspaceEvents() {
    const modal = document.getElementById('delete-workspace-modal');
    const cancelBtn = document.getElementById('cancel-delete-workspace');
    const confirmBtn = document.getElementById('confirm-delete-workspace');

    // 关闭模态框
    const closeModal = () => modal.remove();

    cancelBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });

    // 确认删除
    confirmBtn.addEventListener('click', async () => {
        const workspaceId = confirmBtn.dataset.workspaceId;
        if (workspaceId) {
            await deleteWorkspace(workspaceId);
            closeModal();
        }
    });
}

// 删除工作区
async function deleteWorkspace(workspaceId) {
    try {
        console.log(`🗑️ [DEBUG-UI-DELETE] ========== 开始删除工作区 ==========`);
        console.log(`🗑️ [DEBUG-UI-DELETE] 工作区ID: ${workspaceId}`);

        setLoading(true);
        showNotification('正在删除工作区...', 'info');

        console.log(`📤 [DEBUG-UI-DELETE] 发送删除请求到后台...`);
        const response = await sendMessage('DELETE_WORKSPACE', { workspaceId });
        console.log(`📥 [DEBUG-UI-DELETE] 收到后台响应:`, response);

        if (response.success) {
            console.log(`✅ [DEBUG-UI-DELETE] 删除成功，开始更新UI...`);

            // 重新加载工作区列表
            console.log(`🔄 [DEBUG-UI-DELETE] 重新加载工作区列表...`);
            await loadWorkspaces();

            // 如果删除的是当前工作区，重新加载当前工作区信息
            if (currentWorkspaceId === workspaceId) {
                console.log(`🔄 [DEBUG-UI-DELETE] 删除的是当前工作区，重新加载当前工作区信息...`);
                await loadCurrentWorkspace();
            }

            console.log(`✅ [DEBUG-UI-DELETE] UI更新完成`);
            showNotification('工作区删除成功', 'success');
        } else {
            console.error(`❌ [DEBUG-UI-DELETE] 删除失败:`, response.error);
            showNotification('工作区删除失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        console.error(`❌ [DEBUG-UI-DELETE] 删除工作区异常:`, error);
        console.error(`❌ [DEBUG-UI-DELETE] 错误堆栈:`, error.stack);
        showNotification('工作区删除失败', 'error');
    } finally {
        setLoading(false);
        console.log(`🏁 [DEBUG-UI-DELETE] 删除操作完成`);
    }
}

// 验证固定状态
async function verifyPinningStatus() {
    try {
        setLoading(true);
        showNotification('正在验证固定状态...', 'info');

        const response = await sendMessage('VERIFY_PINNING', { workspaceId: currentWorkspaceId });

        if (response.success) {
            showPinningVerificationResults(response.results, response.summary);
        } else {
            showNotification('验证固定状态失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('验证固定状态失败:', error);
        showNotification('验证固定状态失败', 'error');
    } finally {
        setLoading(false);
    }
}

// 强制应用固定设置
async function forceApplyPinning() {
    try {
        setLoading(true);
        showNotification('正在强制应用固定设置...', 'info');

        const response = await sendMessage('FORCE_APPLY_PINNING', { workspaceId: currentWorkspaceId });

        if (response.success) {
            showNotification('固定设置已强制应用', 'success');
            // 刷新标签页列表以反映变化
            await loadCurrentTabs();
        } else {
            showNotification('强制应用固定设置失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('强制应用固定设置失败:', error);
        showNotification('强制应用固定设置失败', 'error');
    } finally {
        setLoading(false);
    }
}

// 显示固定验证结果
function showPinningVerificationResults(results, summary) {
    // 移除现有结果窗口
    const existingModal = document.getElementById('pinning-verification-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建结果窗口HTML
    const modalHTML = `
        <div id="pinning-verification-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 24px;
                max-width: 600px;
                max-height: 80vh;
                width: 95%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    padding-bottom: 16px;
                    border-bottom: 1px solid #e2e8f0;
                ">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            background: ${summary.incorrect > 0 ? '#fef3c7' : '#dcfce7'};
                            color: ${summary.incorrect > 0 ? '#d97706' : '#16a34a'};
                            border-radius: 8px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 20px;
                        ">${summary.incorrect > 0 ? '⚠️' : '✅'}</div>
                        <div>
                            <h3 style="margin: 0; font-size: 18px; color: #1e293b;">固定状态验证结果</h3>
                            <p style="margin: 4px 0 0 0; font-size: 14px; color: #64748b;">
                                准确率: ${summary.accuracy}% (${summary.correct}/${summary.total})
                            </p>
                        </div>
                    </div>
                    <button id="close-verification-modal" style="
                        background: none;
                        border: none;
                        font-size: 20px;
                        cursor: pointer;
                        padding: 4px;
                        color: #64748b;
                    ">✕</button>
                </div>

                <div style="
                    flex: 1;
                    overflow-y: auto;
                    margin-bottom: 16px;
                ">
                    ${results.filter(r => !r.isCorrect).length > 0 ? `
                        <div style="margin-bottom: 16px;">
                            <h4 style="margin: 0 0 8px 0; color: #dc2626;">需要修复的标签页 (${results.filter(r => !r.isCorrect).length})</h4>
                            ${results.filter(r => !r.isCorrect).map(result => `
                                <div style="
                                    padding: 8px 12px;
                                    border: 1px solid #fecaca;
                                    background: #fef2f2;
                                    border-radius: 6px;
                                    margin-bottom: 8px;
                                ">
                                    <div style="font-weight: 500; font-size: 13px; color: #1e293b;">${result.title}</div>
                                    <div style="font-size: 11px; color: #64748b; margin: 2px 0;">${result.url}</div>
                                    <div style="font-size: 11px; color: #dc2626;">
                                        应该${result.shouldBePinned ? '固定' : '不固定'}，当前${result.currentlyPinned ? '已固定' : '未固定'}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}

                    ${results.filter(r => r.isCorrect).length > 0 ? `
                        <div>
                            <h4 style="margin: 0 0 8px 0; color: #16a34a;">状态正确的标签页 (${results.filter(r => r.isCorrect).length})</h4>
                            <div style="max-height: 200px; overflow-y: auto;">
                                ${results.filter(r => r.isCorrect).map(result => `
                                    <div style="
                                        padding: 6px 12px;
                                        border: 1px solid #bbf7d0;
                                        background: #f0fdf4;
                                        border-radius: 6px;
                                        margin-bottom: 4px;
                                    ">
                                        <div style="font-weight: 500; font-size: 12px; color: #1e293b;">${result.title}</div>
                                        <div style="font-size: 10px; color: #16a34a;">
                                            ${result.currentlyPinned ? '已固定' : '未固定'} ✓
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>

                <div style="
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                    padding-top: 16px;
                    border-top: 1px solid #e2e8f0;
                ">
                    ${summary.incorrect > 0 ? `
                        <button id="fix-pinning-issues" style="
                            padding: 8px 16px;
                            background: #dc2626;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                        ">修复问题</button>
                    ` : ''}
                    <button id="close-verification" style="
                        padding: 8px 16px;
                        background: #e2e8f0;
                        color: #475569;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">关闭</button>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 设置事件监听器
    setupVerificationModalEvents();
}

// 设置验证结果窗口事件
function setupVerificationModalEvents() {
    const modal = document.getElementById('pinning-verification-modal');
    const closeBtn = document.getElementById('close-verification-modal');
    const closeBtn2 = document.getElementById('close-verification');
    const fixBtn = document.getElementById('fix-pinning-issues');

    // 关闭模态框
    const closeModal = () => modal.remove();

    closeBtn?.addEventListener('click', closeModal);
    closeBtn2?.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });

    // 修复问题
    fixBtn?.addEventListener('click', async () => {
        closeModal();
        await forceApplyPinning();
    });
}

// 切换云同步状态
async function toggleCloudSync() {
    try {
        setLoading(true);
        showNotification('正在检查同步状态...', 'info');

        // 获取当前同步状态
        const statusResponse = await sendMessage('GET_SYNC_STATUS');

        if (statusResponse.success) {
            const currentlyEnabled = statusResponse.status.enabled;
            const newState = !currentlyEnabled;

            showNotification(`正在${newState ? '启用' : '禁用'}云同步...`, 'info');

            const response = await sendMessage('ENABLE_CLOUD_SYNC', { enabled: newState });

            if (response.success) {
                showNotification(response.message, 'success');
                updateCloudSyncUI(newState);
            } else {
                showNotification('切换云同步状态失败: ' + (response.error || '未知错误'), 'error');
            }
        } else {
            showNotification('获取同步状态失败: ' + (statusResponse.error || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('切换云同步状态失败:', error);
        showNotification('切换云同步状态失败', 'error');
    } finally {
        setLoading(false);
    }
}

// 显示同步状态
async function showSyncStatus() {
    try {
        setLoading(true);
        showNotification('正在获取同步状态...', 'info');

        const response = await sendMessage('GET_SYNC_STATUS');

        if (response.success) {
            showSyncStatusModal(response.status);
        } else {
            showNotification('获取同步状态失败: ' + (response.error || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('获取同步状态失败:', error);
        showNotification('获取同步状态失败', 'error');
    } finally {
        setLoading(false);
    }
}

// 显示同步状态模态框
function showSyncStatusModal(status) {
    // 移除现有模态框
    const existingModal = document.getElementById('sync-status-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建模态框HTML
    const modalHTML = `
        <div id="sync-status-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 24px;
                max-width: 500px;
                max-height: 80vh;
                width: 95%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    padding-bottom: 16px;
                    border-bottom: 1px solid #e2e8f0;
                ">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            background: ${status.enabled ? '#dcfce7' : '#f3f4f6'};
                            color: ${status.enabled ? '#16a34a' : '#6b7280'};
                            border-radius: 8px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 20px;
                        ">☁️</div>
                        <div>
                            <h3 style="margin: 0; font-size: 18px; color: #1e293b;">云同步状态</h3>
                            <p style="margin: 4px 0 0 0; font-size: 14px; color: #64748b;">
                                ${status.enabled ? '已启用' : '已禁用'}
                            </p>
                        </div>
                    </div>
                    <button id="close-sync-status-modal" style="
                        background: none;
                        border: none;
                        font-size: 20px;
                        cursor: pointer;
                        padding: 4px;
                        color: #64748b;
                    ">✕</button>
                </div>

                <div style="
                    flex: 1;
                    overflow-y: auto;
                    margin-bottom: 16px;
                ">
                    <div style="margin-bottom: 16px;">
                        <h4 style="margin: 0 0 8px 0; color: #1e293b;">设备信息</h4>
                        <div style="
                            padding: 12px;
                            background: #f8fafc;
                            border-radius: 6px;
                            font-size: 12px;
                            color: #64748b;
                        ">
                            设备ID: ${status.deviceId}<br>
                            最后同步: ${status.lastSyncTime ? new Date(status.lastSyncTime).toLocaleString() : '从未同步'}
                        </div>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <h4 style="margin: 0 0 8px 0; color: #1e293b;">本地工作区 (${status.localWorkspaces.length})</h4>
                        <div style="max-height: 120px; overflow-y: auto;">
                            ${status.localWorkspaces.map(workspaceId => `
                                <div style="
                                    padding: 8px 12px;
                                    border: 1px solid #e2e8f0;
                                    border-radius: 4px;
                                    margin-bottom: 4px;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                ">
                                    <span style="font-size: 13px;">${workspaceId}</span>
                                    <button class="sync-workspace-btn" data-workspace-id="${workspaceId}" style="
                                        padding: 2px 6px;
                                        background: #3b82f6;
                                        color: white;
                                        border: none;
                                        border-radius: 3px;
                                        cursor: pointer;
                                        font-size: 10px;
                                    ">同步</button>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div>
                        <h4 style="margin: 0 0 8px 0; color: #1e293b;">云端工作区 (${status.cloudWorkspaces.length})</h4>
                        <div style="max-height: 120px; overflow-y: auto;">
                            ${status.cloudWorkspaces.map(cloudWorkspace => `
                                <div style="
                                    padding: 8px 12px;
                                    border: 1px solid #e2e8f0;
                                    border-radius: 4px;
                                    margin-bottom: 4px;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                ">
                                    <div>
                                        <div style="font-size: 13px;">${cloudWorkspace.workspaceId}</div>
                                        <div style="font-size: 10px; color: #64748b;">
                                            ${new Date(cloudWorkspace.timestamp).toLocaleString()}
                                        </div>
                                    </div>
                                    <button class="restore-workspace-btn" data-workspace-id="${cloudWorkspace.workspaceId}" style="
                                        padding: 2px 6px;
                                        background: #10b981;
                                        color: white;
                                        border: none;
                                        border-radius: 3px;
                                        cursor: pointer;
                                        font-size: 10px;
                                    ">恢复</button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div style="
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                    padding-top: 16px;
                    border-top: 1px solid #e2e8f0;
                ">
                    <button id="toggle-cloud-sync" data-enabled="${status.enabled}" style="
                        padding: 8px 16px;
                        background: ${status.enabled ? '#ef4444' : '#3b82f6'};
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">${status.enabled ? '禁用同步' : '启用同步'}</button>
                    <button id="close-sync-status" style="
                        padding: 8px 16px;
                        background: #e2e8f0;
                        color: #475569;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">关闭</button>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 设置事件监听器
    setupSyncStatusModalEvents();
}

// 设置同步状态模态框事件
function setupSyncStatusModalEvents() {
    const modal = document.getElementById('sync-status-modal');
    const closeBtn = document.getElementById('close-sync-status-modal');
    const closeBtn2 = document.getElementById('close-sync-status');
    const toggleBtn = document.getElementById('toggle-cloud-sync');

    // 关闭模态框
    const closeModal = () => modal.remove();

    closeBtn?.addEventListener('click', closeModal);
    closeBtn2?.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });

    // 切换云同步
    toggleBtn?.addEventListener('click', async () => {
        const enabled = toggleBtn.dataset.enabled === 'true';
        closeModal();

        const response = await sendMessage('ENABLE_CLOUD_SYNC', { enabled: !enabled });
        if (response.success) {
            showNotification(response.message, 'success');
            updateCloudSyncUI(!enabled);
        } else {
            showNotification('切换云同步状态失败: ' + (response.error || '未知错误'), 'error');
        }
    });

    // 同步工作区按钮
    const syncButtons = modal.querySelectorAll('.sync-workspace-btn');
    syncButtons.forEach(button => {
        button.addEventListener('click', async () => {
            const workspaceId = button.dataset.workspaceId;
            if (workspaceId) {
                const response = await sendMessage('SYNC_WORKSPACE_TO_CLOUD', { workspaceId });
                if (response.success) {
                    showNotification('工作区已同步到云端', 'success');
                } else {
                    showNotification('同步失败: ' + (response.error || '未知错误'), 'error');
                }
            }
        });
    });

    // 恢复工作区按钮
    const restoreButtons = modal.querySelectorAll('.restore-workspace-btn');
    restoreButtons.forEach(button => {
        button.addEventListener('click', async () => {
            const workspaceId = button.dataset.workspaceId;
            if (workspaceId) {
                const response = await sendMessage('RESTORE_WORKSPACE_FROM_CLOUD', { workspaceId });
                if (response.success) {
                    showNotification('工作区已从云端恢复', 'success');
                    await loadWorkspaces(); // 刷新工作区列表
                } else {
                    showNotification('恢复失败: ' + (response.error || '未知错误'), 'error');
                }
            }
        });
    });
}

// 更新云同步UI状态
function updateCloudSyncUI(enabled) {
    const cloudSyncBtn = document.getElementById('cloud-sync-btn');
    if (cloudSyncBtn) {
        cloudSyncBtn.textContent = enabled ? '☁️ 已启用' : '☁️ 启用同步';
        cloudSyncBtn.style.background = enabled ? '#10b981' : '#6b7280';
    }
}

// 全局函数（供HTML调用）
window.switchToWorkspace = async function(workspaceId) {
    try {
        setLoading(true);
        const response = await sendMessage('SWITCH_WORKSPACE', { workspaceId });
        if (response.success) {
            currentWorkspaceId = workspaceId;
            await loadInitialData();
            showNotification('工作区切换成功', 'success');
        } else {
            showNotification('工作区切换失败', 'error');
        }
    } catch (error) {
        console.error('切换工作区失败:', error);
        showNotification('工作区切换失败', 'error');
    } finally {
        setLoading(false);
    }
};

// 查看工作区
window.viewWorkspace = function(workspaceId) {
    const workspace = allWorkspaces.find(w => w.id === workspaceId);
    if (!workspace) {
        showNotification('工作区不存在', 'error');
        return;
    }

    showWorkspaceViewModal(workspace);
};

// 显示工作区查看模态框
function showWorkspaceViewModal(workspace) {
    // 移除现有模态框
    const existingModal = document.getElementById('workspace-view-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 计算标签页总数
    const totalTabs = workspace.groups?.reduce((total, group) => total + (group.tabs?.length || 0), 0) || 0;

    // 创建模态框HTML
    const modalHTML = `
        <div id="workspace-view-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 24px;
                max-width: 700px;
                max-height: 85vh;
                width: 95%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                line-height: 1.5;
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    padding-bottom: 16px;
                    border-bottom: 1px solid #e2e8f0;
                ">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            background: ${workspace.color}20;
                            color: ${workspace.color};
                            border-radius: 8px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 20px;
                        ">${workspace.icon}</div>
                        <div>
                            <h3 style="margin: 0; font-size: 18px; color: #1e293b;">${workspace.name}</h3>
                            <p style="margin: 4px 0 0 0; font-size: 14px; color: #64748b;">${totalTabs} 个标签页</p>
                        </div>
                    </div>
                    <button id="close-workspace-view" style="
                        background: none;
                        border: none;
                        font-size: 20px;
                        cursor: pointer;
                        padding: 4px;
                        color: #64748b;
                    ">✕</button>
                </div>

                <div style="
                    flex: 1;
                    overflow-y: auto;
                    margin-bottom: 16px;
                ">
                    ${workspace.groups && workspace.groups.length > 0 ?
                        workspace.groups.map(group => `
                            <div style="
                                border: 1px solid #e2e8f0;
                                border-radius: 6px;
                                margin-bottom: 12px;
                                overflow: hidden;
                            ">
                                <div style="
                                    background: #f8fafc;
                                    padding: 12px 16px;
                                    border-bottom: 1px solid #e2e8f0;
                                    font-weight: 500;
                                    font-size: 14px;
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                ">
                                    <span style="
                                        display: inline-block;
                                        width: 12px;
                                        height: 12px;
                                        background: ${getGroupColorHex(group.color)};
                                        border-radius: 2px;
                                    "></span>
                                    ${group.name} (${group.tabs?.length || 0} 个标签页)
                                </div>
                                <div style="padding: 12px 16px;">
                                    ${group.tabs && group.tabs.length > 0 ?
                                        group.tabs.map(tab => `
                                            <div style="
                                                display: flex;
                                                align-items: center;
                                                gap: 8px;
                                                padding: 8px 0;
                                                border-bottom: 1px solid #f1f5f9;
                                            ">
                                                <input type="checkbox" class="tab-checkbox" data-tab-id="${tab.id}" style="margin: 0;">
                                                <div style="flex: 1; min-width: 0;">
                                                    <div style="
                                                        font-size: 13px;
                                                        font-weight: 500;
                                                        color: #374151;
                                                        white-space: nowrap;
                                                        overflow: hidden;
                                                        text-overflow: ellipsis;
                                                        max-width: 300px;
                                                        word-break: break-all;
                                                    ">${tab.title}</div>
                                                    <div style="
                                                        font-size: 11px;
                                                        color: #64748b;
                                                        white-space: nowrap;
                                                        overflow: hidden;
                                                        text-overflow: ellipsis;
                                                        max-width: 300px;
                                                        word-break: break-all;
                                                    ">${tab.url}</div>
                                                </div>
                                                <div style="display: flex; gap: 4px;">
                                                    <button class="pin-tab-btn" data-tab-id="${tab.id}" data-pinned="${tab.pinned || false}" style="
                                                        padding: 4px 8px;
                                                        background: ${tab.pinned ? '#fef3c7' : '#f3f4f6'};
                                                        color: ${tab.pinned ? '#d97706' : '#6b7280'};
                                                        border: 1px solid ${tab.pinned ? '#fbbf24' : '#d1d5db'};
                                                        border-radius: 4px;
                                                        cursor: pointer;
                                                        font-size: 11px;
                                                    ">${tab.pinned ? '📌' : '📍'}</button>
                                                    <button class="open-tab-btn" data-url="${tab.url}" style="
                                                        padding: 4px 8px;
                                                        background: #f0f9ff;
                                                        color: #0369a1;
                                                        border: 1px solid #0ea5e9;
                                                        border-radius: 4px;
                                                        cursor: pointer;
                                                        font-size: 11px;
                                                    ">打开</button>
                                                    <button class="delete-tab-btn" data-tab-id="${tab.id}" data-workspace-id="${workspace.id}" style="
                                                        padding: 4px 8px;
                                                        background: #fef2f2;
                                                        color: #dc2626;
                                                        border: 1px solid #ef4444;
                                                        border-radius: 4px;
                                                        cursor: pointer;
                                                        font-size: 11px;
                                                    ">删除</button>
                                                </div>
                                            </div>
                                        `).join('') :
                                        '<div style="color: #64748b; font-size: 12px; padding: 8px 0;">暂无标签页</div>'
                                    }
                                </div>
                            </div>
                        `).join('') :
                        '<div style="text-align: center; color: #64748b; padding: 40px;">暂无分组</div>'
                    }
                </div>

                <div style="
                    display: flex;
                    gap: 12px;
                    justify-content: space-between;
                    align-items: center;
                    padding-top: 16px;
                    border-top: 1px solid #e2e8f0;
                ">
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <input type="checkbox" id="select-all-tabs" style="margin: 0;">
                        <label for="select-all-tabs" style="font-size: 12px; color: #64748b;">全选</label>
                        <button class="batch-delete-btn" data-workspace-id="${workspace.id}" style="
                            padding: 6px 12px;
                            background: #ef4444;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                            font-weight: 500;
                        ">批量删除</button>
                    </div>
                    <button class="switch-to-workspace-btn" data-workspace-id="${workspace.id}" style="
                        padding: 8px 16px;
                        background: #3b82f6;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">切换到此工作区</button>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 设置事件监听器
    setupWorkspaceViewEvents();
}

// 设置工作区查看事件
function setupWorkspaceViewEvents() {
    const modal = document.getElementById('workspace-view-modal');
    const closeBtn = document.getElementById('close-workspace-view');

    // 关闭模态框
    const closeModal = () => modal.remove();
    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });

    // 绑定打开标签页按钮事件
    const openTabButtons = modal.querySelectorAll('.open-tab-btn');
    openTabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const url = button.dataset.url;
            if (url && window.openTabInWorkspace) {
                window.openTabInWorkspace(url);
            }
        });
    });

    // 绑定删除标签页按钮事件
    const deleteTabButtons = modal.querySelectorAll('.delete-tab-btn');
    deleteTabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tabId;
            const workspaceId = button.dataset.workspaceId;
            if (tabId && workspaceId) {
                deleteTabFromWorkspace(tabId, workspaceId);
            }
        });
    });

    // 绑定批量删除按钮事件
    const batchDeleteBtn = modal.querySelector('.batch-delete-btn');
    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', () => {
            const workspaceId = batchDeleteBtn.dataset.workspaceId;
            const selectedTabs = modal.querySelectorAll('.tab-checkbox:checked');
            const tabIds = Array.from(selectedTabs).map(cb => cb.dataset.tabId);

            if (tabIds.length > 0 && workspaceId) {
                batchDeleteTabsFromWorkspace(tabIds, workspaceId);
            } else {
                showNotification('请选择要删除的标签页', 'warning');
            }
        });
    }

    // 绑定全选复选框事件
    const selectAllCheckbox = modal.querySelector('#select-all-tabs');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', () => {
            const tabCheckboxes = modal.querySelectorAll('.tab-checkbox');
            tabCheckboxes.forEach(cb => {
                cb.checked = selectAllCheckbox.checked;
            });
        });
    }

    // 绑定固定按钮事件
    const pinTabButtons = modal.querySelectorAll('.pin-tab-btn');
    pinTabButtons.forEach(button => {
        button.addEventListener('click', async () => {
            const tabId = parseInt(button.dataset.tabId);
            const isPinned = button.dataset.pinned === 'true';

            if (tabId) {
                try {
                    await chrome.tabs.update(tabId, { pinned: !isPinned });

                    // 更新按钮状态
                    button.dataset.pinned = (!isPinned).toString();
                    button.textContent = !isPinned ? '📌' : '📍';
                    button.style.background = !isPinned ? '#fef3c7' : '#f3f4f6';
                    button.style.color = !isPinned ? '#d97706' : '#6b7280';
                    button.style.borderColor = !isPinned ? '#fbbf24' : '#d1d5db';

                    showNotification(!isPinned ? '标签页已固定' : '标签页已取消固定', 'success');
                } catch (error) {
                    console.error('切换固定状态失败:', error);
                    showNotification('切换固定状态失败', 'error');
                }
            }
        });
    });

    // 绑定切换工作区按钮事件
    const switchButton = modal.querySelector('.switch-to-workspace-btn');
    if (switchButton) {
        switchButton.addEventListener('click', () => {
            const workspaceId = switchButton.dataset.workspaceId;
            if (workspaceId && window.switchToWorkspace) {
                window.switchToWorkspace(workspaceId);
                modal.remove();
            }
        });
    }
}

// 在工作区中打开标签页
window.openTabInWorkspace = async function(url) {
    try {
        await chrome.tabs.create({ url, active: true });
        showNotification('标签页已打开', 'success');
    } catch (error) {
        console.error('打开标签页失败:', error);
        showNotification('打开标签页失败', 'error');
    }
};

// 切换到指定标签页
window.switchToTab = async function(tabId) {
    try {
        await chrome.tabs.update(tabId, { active: true });
        showNotification('已切换到标签页', 'success');
    } catch (error) {
        console.error('切换标签页失败:', error);
        showNotification('切换标签页失败', 'error');
    }
};

// 切换标签页固定状态（增强版本，支持状态验证和同步）
async function toggleTabPinned(tabId, pinned) {
    try {
        console.log(`🔄 切换标签页 ${tabId} 固定状态: ${pinned ? '固定' : '取消固定'}`);

        // 阶段1: 执行Chrome API操作
        await chrome.tabs.update(tabId, { pinned });
        console.log('✅ Chrome API操作完成');

        // 阶段2: 验证操作是否成功
        const verificationResult = await verifyTabPinningOperation(tabId, pinned);

        if (!verificationResult.success) {
            throw new Error(`固定状态验证失败: ${verificationResult.error}`);
        }

        // 阶段3: 立即更新UI状态（不等待完整刷新）
        updateTabPinningUI(tabId, pinned);

        // 阶段4: 异步刷新标签页列表以确保完整同步
        setTimeout(async () => {
            try {
                await loadCurrentTabs();
                console.log('✅ 标签页列表已刷新');
            } catch (refreshError) {
                console.warn('刷新标签页列表失败:', refreshError);
            }
        }, 500);

        showNotification(pinned ? '标签页已固定' : '标签页已取消固定', 'success');

    } catch (error) {
        console.error('❌ 切换固定状态失败:', error);
        showNotification('切换固定状态失败: ' + error.message, 'error');

        // 尝试恢复UI状态
        await recoverTabPinningUI(tabId);
    }
}

// 验证标签页固定操作
async function verifyTabPinningOperation(tabId, expectedPinned) {
    try {
        // 等待一小段时间让操作生效
        await new Promise(resolve => setTimeout(resolve, 200));

        // 从Chrome API获取实际状态
        const actualTab = await chrome.tabs.get(tabId);
        const actualPinned = actualTab.pinned || false;

        if (actualPinned === expectedPinned) {
            return { success: true };
        } else {
            return {
                success: false,
                error: `期望状态: ${expectedPinned}, 实际状态: ${actualPinned}`
            };
        }
    } catch (error) {
        return {
            success: false,
            error: `验证失败: ${error.message}`
        };
    }
}

// 更新标签页固定UI状态
function updateTabPinningUI(tabId, pinned) {
    try {
        // 更新主列表中的固定按钮
        const pinButton = document.querySelector(`[data-tab-id="${tabId}"].tab-pin-btn`);
        if (pinButton) {
            pinButton.dataset.pinned = pinned.toString();
            pinButton.title = pinned ? '取消固定' : '固定标签页';
            pinButton.classList.toggle('pinned', pinned);

            const pinIcon = pinButton.querySelector('.pin-icon');
            if (pinIcon) {
                pinIcon.textContent = pinned ? '📌' : '📍';
            }

            console.log(`✅ 已更新标签页 ${tabId} 的UI状态`);
        }

        // 更新工作区查看模态框中的按钮（如果存在）
        const modalPinButton = document.querySelector(`#workspace-view-modal [data-tab-id="${tabId}"].pin-tab-btn`);
        if (modalPinButton) {
            modalPinButton.dataset.pinned = pinned.toString();
            modalPinButton.textContent = pinned ? '📌' : '📍';
            modalPinButton.style.background = pinned ? '#fef3c7' : '#f3f4f6';
            modalPinButton.style.color = pinned ? '#d97706' : '#6b7280';
            modalPinButton.style.borderColor = pinned ? '#fbbf24' : '#d1d5db';
        }

        // 更新全局currentTabs状态
        const tabIndex = currentTabs.findIndex(tab => tab.id === tabId);
        if (tabIndex !== -1) {
            currentTabs[tabIndex].pinned = pinned;
            currentTabs[tabIndex]._lastUpdated = Date.now();
        }

    } catch (error) {
        console.error('更新固定UI状态失败:', error);
    }
}

// 恢复标签页固定UI状态
async function recoverTabPinningUI(tabId) {
    try {
        console.log(`🔄 恢复标签页 ${tabId} 的UI状态...`);

        // 从Chrome API获取实际状态
        const actualTab = await chrome.tabs.get(tabId);
        const actualPinned = actualTab.pinned || false;

        // 恢复UI状态
        updateTabPinningUI(tabId, actualPinned);

        console.log(`✅ 已恢复标签页 ${tabId} 的UI状态为: ${actualPinned ? '固定' : '未固定'}`);

    } catch (error) {
        console.error('恢复UI状态失败:', error);
    }
}

// 删除单个标签页
async function deleteTabFromWorkspace(tabId, workspaceId) {
    if (!confirm('确定要删除这个标签页吗？')) {
        return;
    }

    try {
        const response = await sendMessage('DELETE_TAB_FROM_WORKSPACE', {
            tabId,
            workspaceId
        });

        if (response.success) {
            showNotification('标签页已删除', 'success');
            // 重新打开工作区查看页面以刷新数据
            const workspace = allWorkspaces.find(w => w.id === workspaceId);
            if (workspace) {
                document.getElementById('workspace-view-modal').remove();
                setTimeout(() => showWorkspaceViewModal(workspace), 100);
            }
        } else {
            showNotification('删除标签页失败', 'error');
        }
    } catch (error) {
        console.error('删除标签页失败:', error);
        showNotification('删除标签页失败', 'error');
    }
}

// 批量删除标签页
async function batchDeleteTabsFromWorkspace(tabIds, workspaceId) {
    if (!confirm(`确定要删除选中的 ${tabIds.length} 个标签页吗？`)) {
        return;
    }

    try {
        const response = await sendMessage('BATCH_DELETE_TABS_FROM_WORKSPACE', {
            tabIds,
            workspaceId
        });

        if (response.success) {
            showNotification(`已删除 ${tabIds.length} 个标签页`, 'success');
            // 重新打开工作区查看页面以刷新数据
            const workspace = allWorkspaces.find(w => w.id === workspaceId);
            if (workspace) {
                document.getElementById('workspace-view-modal').remove();
                setTimeout(() => showWorkspaceViewModal(workspace), 100);
            }
        } else {
            showNotification('批量删除失败', 'error');
        }
    } catch (error) {
        console.error('批量删除失败:', error);
        showNotification('批量删除失败', 'error');
    }
}

window.switchToTab = async function(tabId) {
    try {
        await chrome.tabs.update(tabId, { active: true });
    } catch (error) {
        console.error('切换标签页失败:', error);
        showNotification('切换标签页失败', 'error');
    }
};

// 创建新工作区
async function createNewWorkspace() {
    showCreateWorkspaceModal();
}

// 显示创建工作区模态框
function showCreateWorkspaceModal() {
    // 移除现有模态框
    const existingModal = document.getElementById('create-workspace-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建模态框HTML
    const modalHTML = `
        <div id="create-workspace-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 24px;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                ">
                    <h3 style="margin: 0; font-size: 18px; color: #1e293b;">创建新工作区</h3>
                    <button id="close-create-modal" style="
                        background: none;
                        border: none;
                        font-size: 20px;
                        cursor: pointer;
                        padding: 4px;
                        color: #64748b;
                    ">✕</button>
                </div>

                <form id="create-workspace-form">
                    <div style="margin-bottom: 16px;">
                        <label style="
                            display: block;
                            margin-bottom: 6px;
                            font-size: 14px;
                            font-weight: 500;
                            color: #374151;
                        ">工作区名称</label>
                        <input type="text" id="workspace-name" required style="
                            width: 100%;
                            padding: 10px 12px;
                            border: 1px solid #e2e8f0;
                            border-radius: 6px;
                            font-size: 14px;
                            box-sizing: border-box;
                        " placeholder="输入工作区名称">
                    </div>

                    <div style="margin-bottom: 16px;">
                        <label style="
                            display: block;
                            margin-bottom: 6px;
                            font-size: 14px;
                            font-weight: 500;
                            color: #374151;
                        ">图标</label>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                            ${['🏠', '📁', '💼', '🎯', '🚀', '⭐', '🔥', '💡'].map(icon => `
                                <button type="button" class="icon-option" data-icon="${icon}" style="
                                    padding: 8px;
                                    border: 1px solid #e2e8f0;
                                    border-radius: 6px;
                                    background: white;
                                    cursor: pointer;
                                    font-size: 16px;
                                    transition: all 0.2s ease;
                                ">${icon}</button>
                            `).join('')}
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="
                            display: block;
                            margin-bottom: 6px;
                            font-size: 14px;
                            font-weight: 500;
                            color: #374151;
                        ">颜色</label>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                            ${['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'].map(color => `
                                <button type="button" class="color-option" data-color="${color}" style="
                                    width: 24px;
                                    height: 24px;
                                    border: 2px solid #e2e8f0;
                                    border-radius: 4px;
                                    background: ${color};
                                    cursor: pointer;
                                    transition: all 0.2s ease;
                                "></button>
                            `).join('')}
                        </div>
                    </div>

                    <div style="
                        display: flex;
                        gap: 12px;
                        justify-content: flex-end;
                    ">
                        <button type="button" id="cancel-create" style="
                            padding: 10px 20px;
                            background: #e2e8f0;
                            color: #475569;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                        ">取消</button>
                        <button type="submit" style="
                            padding: 10px 20px;
                            background: #3b82f6;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                        ">创建工作区</button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 设置事件监听器
    setupCreateWorkspaceEvents();
}

// 设置创建工作区事件
function setupCreateWorkspaceEvents() {
    const modal = document.getElementById('create-workspace-modal');
    const closeBtn = document.getElementById('close-create-modal');
    const cancelBtn = document.getElementById('cancel-create');
    const form = document.getElementById('create-workspace-form');
    const nameInput = document.getElementById('workspace-name');

    let selectedIcon = '📁';
    let selectedColor = '#3b82f6';

    // 关闭模态框
    const closeModal = () => modal.remove();
    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });

    // 图标选择
    const iconOptions = modal.querySelectorAll('.icon-option');
    iconOptions.forEach(option => {
        option.addEventListener('click', () => {
            iconOptions.forEach(opt => opt.style.background = 'white');
            option.style.background = '#f0f9ff';
            selectedIcon = option.dataset.icon;
        });
    });

    // 颜色选择
    const colorOptions = modal.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', () => {
            colorOptions.forEach(opt => opt.style.borderColor = '#e2e8f0');
            option.style.borderColor = '#1e293b';
            selectedColor = option.dataset.color;
        });
    });

    // 表单提交
    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        const name = nameInput.value.trim();
        if (!name) {
            nameInput.focus();
            return;
        }

        try {
            closeModal();
            setLoading(true);
            showNotification('正在创建工作区...', 'info');

            const response = await sendMessage('CREATE_WORKSPACE', {
                name,
                icon: selectedIcon,
                color: selectedColor
            });

            if (response.success) {
                await loadWorkspaces();
                showNotification('工作区创建成功', 'success');
            } else {
                showNotification('工作区创建失败: ' + (response.error || '未知错误'), 'error');
            }
        } catch (error) {
            console.error('创建工作区失败:', error);
            showNotification('工作区创建失败', 'error');
        } finally {
            setLoading(false);
        }
    });

    // 自动聚焦到名称输入框
    nameInput.focus();
}

// AI自动分组
async function performAIGrouping() {
    try {
        setLoading(true);
        showNotification('AI正在分析标签页...', 'info');

        // 获取分组预览
        const response = await sendMessage('AI_AUTO_GROUP_PREVIEW', {
            workspaceId: currentWorkspaceId
        });

        if (response.success && response.groups) {
            // 显示分组预览
            showGroupingPreview(response.groups);
        } else {
            showNotification('AI分组分析失败', 'error');
        }
    } catch (error) {
        console.error('AI分组失败:', error);
        showNotification('AI分组失败', 'error');
    } finally {
        setLoading(false);
    }
}

// 显示分组预览
function showGroupingPreview(groups) {
    // 移除现有预览
    const existingPreview = document.getElementById('grouping-preview');
    if (existingPreview) {
        existingPreview.remove();
    }

    // 创建预览模态框
    const previewHTML = `
        <div id="grouping-preview" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 20px;
                max-width: 500px;
                max-height: 600px;
                width: 90%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16px;
                ">
                    <h3 style="margin: 0; font-size: 16px;">🤖 AI分组预览</h3>
                    <button id="close-preview" style="
                        background: none;
                        border: none;
                        font-size: 18px;
                        cursor: pointer;
                        padding: 4px;
                    ">✕</button>
                </div>

                <div style="
                    flex: 1;
                    overflow-y: auto;
                    margin-bottom: 16px;
                ">
                    ${groups.map(group => `
                        <div style="
                            border: 1px solid #e2e8f0;
                            border-radius: 6px;
                            margin-bottom: 12px;
                            overflow: hidden;
                        ">
                            <div style="
                                background: #f8fafc;
                                padding: 10px 12px;
                                border-bottom: 1px solid #e2e8f0;
                                font-weight: 500;
                                font-size: 14px;
                            ">
                                <span style="
                                    display: inline-block;
                                    width: 12px;
                                    height: 12px;
                                    background: ${getGroupColorHex(group.color)};
                                    border-radius: 2px;
                                    margin-right: 8px;
                                "></span>
                                ${group.name} (${group.tabs.length} 个标签页)
                            </div>
                            <div style="padding: 8px 12px;">
                                ${group.tabs.map(tab => `
                                    <div style="
                                        font-size: 12px;
                                        padding: 4px 0;
                                        border-bottom: 1px solid #f1f5f9;
                                    ">
                                        ${tab.title}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `).join('')}
                </div>

                <div style="
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                ">
                    <button id="cancel-grouping" style="
                        padding: 8px 16px;
                        background: #e2e8f0;
                        color: #475569;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">取消</button>
                    <button id="confirm-grouping" style="
                        padding: 8px 16px;
                        background: #3b82f6;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">确认分组</button>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', previewHTML);

    // 设置事件监听器
    setupPreviewEvents(groups);
}

// 设置预览事件
function setupPreviewEvents(groups) {
    const preview = document.getElementById('grouping-preview');
    const closeBtn = document.getElementById('close-preview');
    const cancelBtn = document.getElementById('cancel-grouping');
    const confirmBtn = document.getElementById('confirm-grouping');

    // 关闭预览
    const closePreview = () => preview.remove();
    closeBtn.addEventListener('click', closePreview);
    cancelBtn.addEventListener('click', closePreview);
    preview.addEventListener('click', (e) => {
        if (e.target === preview) closePreview();
    });

    // 确认分组
    confirmBtn.addEventListener('click', async () => {
        try {
            closePreview();
            setLoading(true);
            showNotification('正在应用分组...', 'info');

            const response = await sendMessage('AI_AUTO_GROUP_APPLY', {
                workspaceId: currentWorkspaceId,
                groups: groups
            });

            if (response.success) {
                await loadCurrentTabs();
                showNotification('AI分组完成', 'success');
            } else {
                showNotification('应用分组失败', 'error');
            }
        } catch (error) {
            console.error('应用分组失败:', error);
            showNotification('应用分组失败', 'error');
        } finally {
            setLoading(false);
        }
    });
}

// 获取分组颜色的十六进制值
function getGroupColorHex(color) {
    const colorMap = {
        'blue': '#3b82f6',
        'purple': '#8b5cf6',
        'pink': '#ec4899',
        'red': '#ef4444',
        'orange': '#f97316',
        'green': '#10b981',
        'grey': '#6b7280'
    };
    return colorMap[color] || '#6b7280';
}

// 添加标签页
async function addTabByMethod(method) {
    switch (method) {
        case 'url':
            showAddUrlModal();
            break;
        case 'bookmark':
            await showBookmarkSelector();
            break;
        case 'current':
            await addCurrentTabToWorkspace();
            break;
    }
}

// 显示添加URL模态框
function showAddUrlModal() {
    // 移除现有模态框
    const existingModal = document.getElementById('add-url-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建模态框HTML
    const modalHTML = `
        <div id="add-url-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 24px;
                max-width: 500px;
                width: 90%;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                ">
                    <h3 style="margin: 0; font-size: 18px; color: #1e293b;">添加网站</h3>
                    <button id="close-url-modal" style="
                        background: none;
                        border: none;
                        font-size: 20px;
                        cursor: pointer;
                        padding: 4px;
                        color: #64748b;
                    ">✕</button>
                </div>

                <form id="add-url-form">
                    <div style="margin-bottom: 16px;">
                        <label style="
                            display: block;
                            margin-bottom: 6px;
                            font-size: 14px;
                            font-weight: 500;
                            color: #374151;
                        ">网站地址</label>
                        <input type="url" id="website-url" required style="
                            width: 100%;
                            padding: 10px 12px;
                            border: 1px solid #e2e8f0;
                            border-radius: 6px;
                            font-size: 14px;
                            box-sizing: border-box;
                        " placeholder="https://example.com">
                    </div>

                    <div style="
                        display: flex;
                        gap: 12px;
                        justify-content: flex-end;
                        margin-top: 20px;
                    ">
                        <button type="button" id="cancel-url" style="
                            padding: 10px 20px;
                            background: #e2e8f0;
                            color: #475569;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                        ">取消</button>
                        <button type="button" id="ai-analyze-url" style="
                            padding: 10px 20px;
                            background: #8b5cf6;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                        ">🤖 AI创建</button>
                        <button type="submit" style="
                            padding: 10px 20px;
                            background: #3b82f6;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                        ">手动添加</button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 设置事件监听器
    setupAddUrlEvents();
}

// 设置添加URL事件
function setupAddUrlEvents() {
    const modal = document.getElementById('add-url-modal');
    const closeBtn = document.getElementById('close-url-modal');
    const cancelBtn = document.getElementById('cancel-url');
    const aiAnalyzeBtn = document.getElementById('ai-analyze-url');
    const form = document.getElementById('add-url-form');
    const urlInput = document.getElementById('website-url');

    // 关闭模态框
    const closeModal = () => modal.remove();
    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });

    // AI分析按钮
    aiAnalyzeBtn.addEventListener('click', async () => {
        const url = urlInput.value.trim();
        if (!url) {
            urlInput.focus();
            showNotification('请输入网址', 'warning');
            return;
        }

        try {
            closeModal();
            setLoading(true);
            showNotification('AI正在分析网站...', 'info');

            const response = await sendMessage('AI_ANALYZE_URL', { url });

            if (response.success) {
                showAIAnalysisResult(url, response);
            } else {
                showNotification('AI分析失败: ' + (response.error || '未知错误'), 'error');
            }
        } catch (error) {
            console.error('AI分析失败:', error);
            showNotification('AI分析失败', 'error');
        } finally {
            setLoading(false);
        }
    });

    // 表单提交（手动添加）
    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        const url = urlInput.value.trim();
        if (!url) {
            urlInput.focus();
            return;
        }

        try {
            closeModal();
            await addTabToWorkspace(url);
        } catch (error) {
            console.error('添加网站失败:', error);
            showNotification('添加网站失败', 'error');
        }
    });

    // 自动聚焦到URL输入框
    urlInput.focus();
}

// 显示AI分析结果
function showAIAnalysisResult(url, analysisResult) {
    // 移除现有模态框
    const existingModal = document.getElementById('ai-analysis-modal');
    if (existingModal) {
        existingModal.remove();
    }

    const { title, suggestedGroup, category } = analysisResult;

    // 创建模态框HTML
    const modalHTML = `
        <div id="ai-analysis-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 24px;
                max-width: 500px;
                width: 90%;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                ">
                    <h3 style="margin: 0; font-size: 18px; color: #1e293b;">🤖 AI分析结果</h3>
                    <button id="close-ai-analysis" style="
                        background: none;
                        border: none;
                        font-size: 20px;
                        cursor: pointer;
                        padding: 4px;
                        color: #64748b;
                    ">✕</button>
                </div>

                <div style="
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 6px;
                    padding: 16px;
                    margin-bottom: 20px;
                ">
                    <div style="
                        font-size: 14px;
                        font-weight: 500;
                        color: #374151;
                        margin-bottom: 8px;
                    ">网站信息</div>
                    <div style="
                        font-size: 16px;
                        font-weight: 600;
                        color: #1e293b;
                        margin-bottom: 4px;
                    ">${title}</div>
                    <div style="
                        font-size: 12px;
                        color: #64748b;
                        word-break: break-all;
                    ">${url}</div>
                </div>

                <div style="
                    background: #f0f9ff;
                    border: 1px solid #0ea5e9;
                    border-radius: 6px;
                    padding: 16px;
                    margin-bottom: 20px;
                ">
                    <div style="
                        font-size: 14px;
                        font-weight: 500;
                        color: #0c4a6e;
                        margin-bottom: 8px;
                    ">AI建议分组</div>
                    <div style="
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    ">
                        <span style="
                            display: inline-block;
                            width: 12px;
                            height: 12px;
                            background: ${getGroupColorHex(suggestedGroup.color)};
                            border-radius: 2px;
                        "></span>
                        <span style="
                            font-size: 14px;
                            font-weight: 500;
                            color: #0369a1;
                        ">${suggestedGroup.name}</span>
                        <span style="
                            font-size: 12px;
                            color: #64748b;
                        ">(${category})</span>
                    </div>
                </div>

                <div style="
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                ">
                    <button id="cancel-ai-analysis" style="
                        padding: 10px 20px;
                        background: #e2e8f0;
                        color: #475569;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">取消</button>
                    <button id="confirm-ai-analysis" style="
                        padding: 10px 20px;
                        background: #10b981;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 500;
                    ">确认添加</button>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 设置事件监听器
    setupAIAnalysisEvents(url, title, suggestedGroup);
}

// 设置AI分析事件
function setupAIAnalysisEvents(url, title, suggestedGroup) {
    const modal = document.getElementById('ai-analysis-modal');
    const closeBtn = document.getElementById('close-ai-analysis');
    const cancelBtn = document.getElementById('cancel-ai-analysis');
    const confirmBtn = document.getElementById('confirm-ai-analysis');

    // 关闭模态框
    const closeModal = () => modal.remove();
    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });

    // 确认添加
    confirmBtn.addEventListener('click', async () => {
        try {
            closeModal();
            setLoading(true);
            showNotification('正在添加到工作区...', 'info');

            // 添加标签页到当前工作区
            await addTabToWorkspace(url, title, suggestedGroup);

            showNotification('标签页添加成功', 'success');
        } catch (error) {
            console.error('添加标签页失败:', error);
            showNotification('添加标签页失败', 'error');
        } finally {
            setLoading(false);
        }
    });
}

// 添加标签页到工作区
async function addTabToWorkspace(url, title = null, suggestedGroup = null) {
    try {
        // 使用目标工作区ID，如果没有指定则使用当前工作区
        const workspaceId = targetWorkspaceId || currentWorkspaceId;

        const response = await sendMessage('ADD_TAB_TO_WORKSPACE', {
            workspaceId,
            url,
            title,
            suggestedGroup
        });

        if (response.success) {
            // 如果添加到当前工作区，刷新标签页列表
            if (workspaceId === currentWorkspaceId) {
                await loadCurrentTabs();
            }

            // 重置目标工作区ID
            targetWorkspaceId = null;

            showNotification(`标签页已添加到工作区`, 'success');
        } else {
            showNotification('标签页添加失败', 'error');
        }
    } catch (error) {
        console.error('添加标签页失败:', error);
        showNotification('添加标签页失败', 'error');
    }
}

// 显示书签选择器
async function showBookmarkSelector() {
    try {
        // 获取所有书签
        const bookmarks = await chrome.bookmarks.getTree();
        const flatBookmarks = flattenBookmarks(bookmarks);

        // 创建书签选择器模态框
        createBookmarkModal(flatBookmarks);
    } catch (error) {
        console.error('获取书签失败:', error);
        showNotification('获取书签失败', 'error');
    }
}

// 扁平化书签树
function flattenBookmarks(bookmarkNodes, result = []) {
    for (const node of bookmarkNodes) {
        if (node.url) {
            // 这是一个书签
            result.push({
                id: node.id,
                title: node.title,
                url: node.url,
                parentTitle: getParentTitle(node)
            });
        }
        if (node.children) {
            // 递归处理子节点
            flattenBookmarks(node.children, result);
        }
    }
    return result;
}

// 获取父文件夹标题
function getParentTitle(node) {
    // 这里可以实现获取父文件夹标题的逻辑
    return '书签栏'; // 简化实现
}

// 创建书签选择器模态框
function createBookmarkModal(bookmarks) {
    // 移除现有模态框
    const existingModal = document.getElementById('bookmark-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建模态框HTML
    const modalHTML = `
        <div id="bookmark-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
            <div style="
                background: white;
                border-radius: 8px;
                padding: 20px;
                max-width: 500px;
                max-height: 600px;
                width: 90%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16px;
                ">
                    <h3 style="margin: 0; font-size: 16px;">选择书签</h3>
                    <button id="close-bookmark-modal" style="
                        background: none;
                        border: none;
                        font-size: 18px;
                        cursor: pointer;
                        padding: 4px;
                    ">✕</button>
                </div>

                <input type="text" id="bookmark-search" placeholder="搜索书签..." style="
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #e2e8f0;
                    border-radius: 6px;
                    margin-bottom: 12px;
                    font-size: 14px;
                ">

                <div id="bookmark-list" style="
                    flex: 1;
                    overflow-y: auto;
                    border: 1px solid #e2e8f0;
                    border-radius: 6px;
                    max-height: 400px;
                ">
                    ${bookmarks.map(bookmark => `
                        <div class="bookmark-item" data-url="${bookmark.url}" style="
                            padding: 12px;
                            border-bottom: 1px solid #f1f5f9;
                            cursor: pointer;
                            transition: background-color 0.2s ease;
                        ">
                            <div style="font-size: 14px; font-weight: 500; margin-bottom: 4px;">
                                ${bookmark.title}
                            </div>
                            <div style="font-size: 12px; color: #64748b;">
                                ${bookmark.url}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 设置事件监听器
    setupBookmarkModalEvents(bookmarks);
}

// 设置书签模态框事件
function setupBookmarkModalEvents(bookmarks) {
    const modal = document.getElementById('bookmark-modal');
    const closeBtn = document.getElementById('close-bookmark-modal');
    const searchInput = document.getElementById('bookmark-search');
    const bookmarkList = document.getElementById('bookmark-list');

    // 关闭模态框
    closeBtn.addEventListener('click', () => modal.remove());
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.remove();
    });

    // 搜索功能
    searchInput.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();
        const filteredBookmarks = bookmarks.filter(bookmark =>
            bookmark.title.toLowerCase().includes(query) ||
            bookmark.url.toLowerCase().includes(query)
        );
        renderBookmarkList(filteredBookmarks);
    });

    // 书签点击事件
    bookmarkList.addEventListener('click', async (e) => {
        const bookmarkItem = e.target.closest('.bookmark-item');
        if (bookmarkItem) {
            const url = bookmarkItem.dataset.url;
            await addTabToWorkspace(url);
            modal.remove();
        }
    });
}

// 渲染书签列表
function renderBookmarkList(bookmarks) {
    const bookmarkList = document.getElementById('bookmark-list');
    bookmarkList.innerHTML = bookmarks.map(bookmark => `
        <div class="bookmark-item" data-url="${bookmark.url}" style="
            padding: 12px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: background-color 0.2s ease;
        ">
            <div style="font-size: 14px; font-weight: 500; margin-bottom: 4px;">
                ${bookmark.title}
            </div>
            <div style="font-size: 12px; color: #64748b;">
                ${bookmark.url}
            </div>
        </div>
    `).join('');
}

// 添加当前标签页到工作区
async function addCurrentTabToWorkspace() {
    try {
        const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (currentTab) {
            await addTabToWorkspace(currentTab.url);
        }
    } catch (error) {
        console.error('添加当前标签页失败:', error);
        showNotification('添加当前标签页失败', 'error');
    }
}
